/* Modern Professional Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    max-width: 900px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-bottom: 40px;
    font-weight: 300;
}

.video-container {
    margin: 30px 0;
    position: relative;
}

#webcam-video {
    border: 3px solid #667eea;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    max-width: 100%;
    width: 100%;
    max-width: 500px;
    height: auto;
}

#capture-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 20px 0;
}

#capture-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2, #667eea);
}

#capture-button:active {
    transform: translateY(0);
}

.display-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;
    align-items: start;
}

.prediction-box, .chatbox {
    background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    text-align: left;
}

.prediction-box h3, .chatbox h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.emotion-item {
    margin: 15px 0;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.emotion-item:hover {
    transform: translateX(5px);
}

.emotion-item strong {
    color: #2c3e50;
    font-size: 1rem;
}

#fused-emotion, #facial-emotion, #speech-emotion {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1rem;
    text-transform: capitalize;
}

.chatbox {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    border: 1px solid rgba(39, 174, 96, 0.2);
    position: relative;
}

.chatbox::before {
    content: "🤖";
    position: absolute;
    top: -10px;
    left: 20px;
    background: white;
    padding: 5px 10px;
    border-radius: 50%;
    font-size: 1.2rem;
}

.chatbox h3 {
    border-bottom-color: #27ae60;
    color: #27ae60;
}

#chatbot-reply {
    color: #2c3e50;
    font-size: 1.1rem;
    line-height: 1.6;
    font-style: italic;
    background: white;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #27ae60;
    margin-top: 15px;
}

/* Loading Animation */
.loading {
    display: none;
    text-align: center;
    margin: 20px 0;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
        align-items: flex-start;
    }

    .container {
        margin: 10px;
        padding: 20px;
    }

    h1 {
        font-size: 2rem;
    }

    .display-area {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    #capture-button {
        padding: 12px 25px;
        font-size: 14px;
    }
}