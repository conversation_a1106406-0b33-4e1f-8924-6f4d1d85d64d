body {
    font-family: sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f4f4f4;
    color: #333;
}

.container {
    text-align: center;
    background-color: #fff;
    padding: 20px 40px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.video-container {
    margin-bottom: 20px;
}

#webcam-video {
    width: 100%;
    max-width: 400px;
    border-radius: 8px;
    border: 2px solid #ccc;
}

#capture-button {
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
}

.display-area {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.prediction-box, .chatbox {
    background-color: #eee;
    padding: 15px;
    border-radius: 8px;
    width: 45%;
    text-align: left;
}

#fused-emotion, #facial-emotion, #speech-emotion {
    font-weight: bold;
    color: #007bff;
}