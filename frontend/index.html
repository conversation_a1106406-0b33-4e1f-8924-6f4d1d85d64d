<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multimodal Emotion Interface</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Multimodal Emotion-to-Engagement Prediction</h1>
        <p class="subtitle">AI-powered emotion detection and intelligent chatbot responses</p>

        <div class="video-container">
            <video id="webcam-video" autoplay></video>
            <canvas id="canvas" style="display: none;"></canvas>
        </div>

        <div class="controls">
            <button id="capture-button">🎯 Capture Emotion</button>
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analyzing emotions...</p>
            </div>
            <p id="status-message">Ready to capture your emotions</p>
        </div>

        <div class="display-area">
            <div class="prediction-box">
                <h3>📊 Emotion Analysis</h3>
                <div class="emotion-item">
                    <strong>Fused Emotion:</strong> <span id="fused-emotion">Waiting for capture...</span>
                </div>
                <div class="emotion-item">
                    <strong>Facial Expression:</strong> <span id="facial-emotion">Waiting for capture...</span>
                </div>
                <div class="emotion-item">
                    <strong>Speech Tone:</strong> <span id="speech-emotion">Waiting for capture...</span>
                </div>
            </div>

            <div class="chatbox">
                <h3>💬 AI Assistant</h3>
                <div id="reply-message">Hello! I'm your AI emotion assistant. Capture your emotions and I'll provide personalized responses to help improve your engagement and well-being.</div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>